/* Base styles */
body {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.container-fluid {
  padding: 0;
  margin: 0;
  position: relative;
}

/* Loader styles */
.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s, visibility 0.5s;
}

.loader {
  width: 120px;
  height: 120px;
  border: 5px solid transparent;
  border-top-color: #f9a825;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
  position: relative;
}

.loader:before,
.loader:after {
  content: "";
  position: absolute;
  border: 5px solid transparent;
  border-radius: 50%;
}

.loader:before {
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border-top-color: #e53935;
  animation: spin 2s linear infinite;
}

.loader:after {
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 15px;
  border-top-color: #64b5f6;
  animation: spin 1s linear infinite;
}

.loader-text {
  margin-top: 30px;
  font-family: "Arial", sans-serif;
  font-size: 28px;
  color: white;
  letter-spacing: 3px;
  position: relative;
}

.loader-text span {
  display: inline-block;
  opacity: 0;
  animation: fadeIn 1s ease-in-out infinite;
}

.loader-text span:nth-child(n + 1) {
  animation-delay: calc(0.05s * var(--n));
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  0%,
  100% {
    opacity: 0;
    transform: translateY(5px);
  }

  50% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Map container */
#mapid {
  height: 100vh;
  width: calc(100% - 300px);
  margin: 0;
  padding: 0;
  position: fixed;
  top: 0;
  left: 300px;
  right: 0;
  bottom: 0;
  z-index: 95;
}

/* Navbar */
.navbar {
  margin-bottom: 0 !important;
  z-index: 2000;
  position: relative;
}

/* Leaflet controls */
.leaflet-control-container .leaflet-top {
  top: 80px;
}

.leaflet-control-container .leaflet-right {
  right: 10px;
}

.leaflet-control-zoom {
  margin-right: 10px;
}

.leaflet-control-attribution {
  display: none !important;
}

/* Sidebar */
.sidebar {
  width: 300px;
  max-width: 300px;
  height: calc(100vh - 70px);
  background-color: white;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: fixed;
  top: 70px;
  left: 0;
  z-index: 1000;
  border-radius: 0;
}

.sidebar-header {
  padding: 12px;
  background-color: #2c3e50;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.sidebar-content {
  padding: 0 8px 8px;
  overflow-y: auto;
  flex: 1;
  position: relative;
}

/* Filter styles */
.filter-group {
  margin-bottom: 6px;
}

.filter-group label {
  display: block;
  margin-bottom: 1px;
  font-weight: 500;
  font-size: 10px;
  color: #555;
}

.filter-group select,
.filter-group input,
#bs-search,
#reset-filters {
  width: 100%;
  padding: 4px 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 11px;
  height: 28px;
}

.search-reset-container {
  display: flex;
  gap: 5px;
  margin-bottom: 6px;
  align-items: flex-end;
}

.search-reset-container .search-input-container {
  flex: 1;
}

.search-reset-container .reset-button-container {
  width: auto;
}

#reset-filters {
  white-space: nowrap;
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
  margin-bottom: 0;
}

#reset-filters:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

/* Stats styles */
.stats-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border-radius: 5px;
  padding: 10px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.stat-item.online .stat-value {
  color: #4caf50;
}

.stat-item.offline .stat-value {
  color: #f44336;
}

/* Downtime legend */
.downtime-legend {
  position: absolute;
  bottom: 20px;
  left: 310px;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 8px;
  border-radius: 6px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
  max-width: 240px;
  font-size: 11px;
}

.downtime-legend h5 {
  margin: 0 0 6px 0;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
}

.downtime-item {
  display: flex;
  align-items: center;
  margin-bottom: 3px;
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.downtime-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.downtime-item.active {
  background-color: rgba(0, 0, 0, 0.1);
}

.color-box {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  margin-right: 6px;
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.downtime-label {
  flex: 1;
}

.downtime-count {
  font-weight: bold;
  margin-left: 5px;
}

.downtime-item.show-all-item {
  border-top: 1px solid #ddd;
  padding-top: 5px;
  margin-top: 5px;
}

/* Map controls */
.map-type-control {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  padding: 3px;
  position: absolute;
  top: 80px;
  right: 10px;
  z-index: 1000;
}

.map-controls-row {
  display: flex;
  flex-direction: row;
  gap: 3px;
}

.map-source-buttons,
.map-type-buttons {
  display: flex;
}

.map-type-control button {
  background-color: white;
  border: none;
  padding: 6px 6px;
  cursor: pointer;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
  color: #555;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-source-logo {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.map-source-buttons button:first-child,
.map-type-buttons button:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.map-source-buttons button:last-child,
.map-type-buttons button:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.map-type-control button.active {
  background-color: #4285f4;
  color: white;
  font-weight: 500;
}

.map-type-control button:hover:not(.active) {
  background-color: #f4f4f4;
}

/* Search styles */
.search-container {
  display: flex;
  margin-bottom: 3px;
  width: 100%;
}

.search-input {
  flex-grow: 1;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 13px;
  height: 36px;
  box-sizing: border-box;
}

.search-button {
  background-color: white;
  border: 1px solid #ddd;
  border-left: none;
  border-radius: 0 4px 4px 0;
  padding: 6px 10px;
  cursor: pointer;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.search-button:hover {
  background-color: #f4f4f4;
}

/* Region stats */
.regions-stats {
  margin-top: 0;
  border-top: none;
  padding-top: 0;
}

.regions-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-top: 0;
  font-size: 12px;
}

.regions-table thead {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.regions-table th {
  background-color: #f3f3f3;
  padding: 6px 5px;
  text-align: center;
  font-weight: 600;
  border-bottom: 2px solid #ddd;
  font-size: 11px;
}

.regions-table td {
  padding: 5px 4px;
  text-align: center;
  border-bottom: 1px solid #eee;
  font-size: 11px;
}

.regions-table tr:hover {
  background-color: #f5f5f5;
  cursor: pointer;
}

.regions-table .region-name-cell {
  text-align: left;
  font-weight: 500;
}

.regions-table tr.active {
  background-color: #e3f2fd;
}

.total-stat {
  color: #333;
}

.online-stat {
  color: #4caf50;
}

.offline-stat {
  color: #f44336;
}

/* BS name tooltip */
.bs-name-tooltip {
  background: transparent;
  border: none;
  box-shadow: none;
  color: #000000;
  font-weight: bold;
  font-size: 10px;
  padding: 0;
  text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  border-radius: 0;
  transition: font-size 0.2s;
}

/* Make BS search label same style as Area label */
#bs-search-label,
#area-label {
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 4px;
  display: inline-block;
}

.zoom-level-low .bs-name-tooltip {
  font-size: 8px;
}

.zoom-level-medium .bs-name-tooltip {
  font-size: 10px;
}

.zoom-level-high .bs-name-tooltip {
  font-size: 12px;
}

.leaflet-tooltip.bs-name-tooltip {
  background: transparent;
  border: none;
  box-shadow: none;
}

.leaflet-tooltip.bs-name-tooltip:before {
  display: none;
}

/* Copyright */
.custom-copyright {
  position: absolute;
  bottom: 5px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 11px;
  color: #333;
  z-index: 1000;
  font-weight: 500;
}

/* Popup styles */
.bs-hover-popup {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  padding: 5px 8px;
  font-size: 12px;
  border: none;
}

.bs-hover-popup .leaflet-popup-content-wrapper {
  background-color: white;
  border-radius: 4px;
  padding: 0;
}

.bs-hover-popup .leaflet-popup-content {
  margin: 0;
  line-height: 1.5;
}

.bs-hover-popup .leaflet-popup-tip {
  background-color: white;
}

/* Language selector */
.language-selector {
  display: flex;
}

.lang-btn {
  background-color: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-left: 5px;
  padding: 2px 6px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
}

.lang-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.lang-btn.active {
  background-color: #f39c12;
  border-color: #f39c12;
  color: #2c3e50;
  font-weight: bold;
}

/* Utility spacing */
.sidebar-content > div:nth-child(2) {
  margin-top: 3px;
}

.regions-table + div {
  margin-top: 3px;
}
