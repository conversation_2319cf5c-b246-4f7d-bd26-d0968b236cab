from bsview.views.imports_file import *

@login_required
def createKMZ(request):
    region = RegionUzb.objects.all()
    return render(request, 'kmzcreate.html', {'region': region})


@login_required
def getKMZ(request):
    # bsnumber = request.POST['bsnumber']
    region = request.POST.get('regiono')
    reg = RegionUzb.objects.get(id=region)
    regionname = request.POST['regiono']
    g900 = "FF" + request.POST.get('g900')[5:] + request.POST.get('g900')[3:5] + request.POST.get('g900')[1:3]
    g1800 = "FF" + request.POST.get('g1800')[5:] + request.POST.get('g1800')[3:5] + request.POST.get('g1800')[1:3]
    u900 = "FF" + request.POST.get('u900')[5:] + request.POST.get('u900')[3:5] + request.POST.get('u900')[1:3]
    u2100 = "FF" + request.POST.get('u2100')[5:] + request.POST.get('u2100')[3:5] + request.POST.get('u2100')[1:3]
    ubesector = "FF" + request.POST.get('ubesector')[5:] + request.POST.get('ubesector')[3:5] + request.POST.get(
        'ubesector')[1:3]
    l800 = "FF" + request.POST.get('l800')[5:] + request.POST.get('l800')[3:5] + request.POST.get('l800')[1:3]
    l1800 = "FF" + request.POST.get('l1800')[5:] + request.POST.get('l1800')[3:5] + request.POST.get('l1800')[1:3]
    l2600 = "FF" + request.POST.get('l2600')[5:] + request.POST.get('l2600')[3:5] + request.POST.get('l2600')[1:3]
    l2300 = "FF" + request.POST.get('l2300')[5:] + request.POST.get('l2300')[3:5] + request.POST.get('l2300')[1:3]
    l2100 = "FF" + request.POST.get('l2100')[5:] + request.POST.get('l2100')[3:5] + request.POST.get('l2100')[1:3]

    Cheks = request.POST.getlist('Chek[]')

    bss = BsBeeline.objects.filter(region_id=region)
    folderpath = 'templates\kml'
    filname = f"kmlfile-{dt.datetime.now().strftime('%d-%m-%Y-%H-%M-%S')}"
    my_kml = open(f'{folderpath}\{filname}.kml', "a+", encoding="utf-8")
    for startkml in startKml(reg):
        my_kml.write(startkml)

    if "1" in Cheks:
        for g900list in createKml(reg, bss, g900, 'gsm900.png', 'GSM 900 Positions', 'Sectors GSM 900', 'gsm900'):
            my_kml.write(g900list)
    if "2" in Cheks:
        for g1800list in createKml(reg, bss, g1800, 'gsm1800.png', 'GSM 1800 Positions', 'Sectors GSM 1800', 'gsm1800'):
            my_kml.write(g1800list)
    if "3" in Cheks:
        for u900list in createKml(reg, bss, u900, 'umts900.png', 'UMTS 900 Positions', 'Sectors UMTS 900', 'umts900'):
            my_kml.write(u900list)
    if "4" in Cheks:
        for u2100list in createKml(reg, bss, u2100, 'umts2100.png', 'UMTS 2100 Positions', 'Sectors UMTS2100',
                                   'umts2100'):
            my_kml.write(u2100list)
    if "5" in Cheks:
        for ubeseclist in createKml(reg, bss, ubesector, 'ubesec.png', 'UMTS BESEC Positions', 'Sectors UMTS BESEC',
                                    'besector'):
            my_kml.write(ubeseclist)
    if "6" in Cheks:
        for l800list in createKml(reg, bss, l800, 'lte800.png', 'LTE 800 Positions', 'Sectors LTE 800', 'lte800'):
            my_kml.write(l800list)
    if "7" in Cheks:
        for l1800list in createKml(reg, bss, l1800, 'lte1800.png', 'LTE 1800 Positions', 'Sectors LTE 1800', 'lte1800'):
            my_kml.write(l1800list)
    if "8" in Cheks:
        for l2600list in createKml(reg, bss, l2600, 'lte2600.png', 'LTE 2600 Positions', 'Sectors LTE 2600', 'lte2600'):
            my_kml.write(l2600list)
    if "9" in Cheks:
        for l2300list in createKml(reg, bss, l2300, 'lte2300.png', 'LTE 2300 Positions', 'Sectors LTE 2300', 'lte2300'):
            my_kml.write(l2300list)
    if "10" in Cheks:
        for l2100list in createKml(reg, bss, l2100, 'lte2100.png', 'LTE 2100 Positions', 'Sectors LTE 2100', 'lte2100'):
            my_kml.write(l2100list)
    for endkml in endKml():
        my_kml.write(endkml)
    my_kml.close()
    # create KMZ from kml
    filenames = []
    filenames.append(f'{folderpath}\{filname}.kml')
    for dirPath, dirNames, fileName in os.walk('templates\pngfiles'):
        for flist in fileName:
            filenames.append(f'templates\pngfiles\{flist}')
    rname = translit(str(reg), 'ru', reversed=True)
    # print(rname)
    zip_filename = f'{rname}-{filname}.kmz'
    byte_data = BytesIO()
    zip_file = zipfile.ZipFile(byte_data, "w")
    for file in filenames:
        filename = os.path.basename(os.path.normpath(file))
        zip_file.write(file, filename)
    zip_file.close()
    response = HttpResponse(byte_data.getvalue(), content_type='application/zip')
    response['Content-Disposition'] = f'attachment; filename={zip_filename}'
    os.remove(f'{folderpath}\{filname}.kml')
    return response


@login_required
def createBTS(request):
    region = RegionUzb.objects.all()
    return render(request, 'btscreate.html', {'region': region})


@login_required
def createGnet(request):
    region = RegionUzb.objects.all()
    return render(request, 'gnetcreate.html', {'region': region})


@login_required
def getGnet(request):
    region = request.POST.get('regiono')
    bss = BsBeeline.objects.filter(region_id=region)
    myfile = bsview.models.ConfFile.objects.latest('id')
    # print(myfile.file2g)
    file_data = cellCreate(bss, myfile)
    response = HttpResponse(file_data, content_type='application/text charset=utf-8')
    response['Content-Disposition'] = f'attachment; filename = cellfile.txt'
    return response


@login_required
def getBTS(request):
    region = request.POST.get('regiono')
    reg = RegionUzb.objects.all()
    if region == '16':
        bss = BsBeeline.objects.all()
    else:
        bss = BsBeeline.objects.filter(region_id=region)
    if request.method == "POST":
        rowsFile = []
        bsName = []
        bsnomer = []
        cellName = []
        channel = []
        bsic = []
        cid = []
        lac = []
        band = []
        tac = []
        # GSM -------------------------------------------------------------------------------------------------------
        if request.POST['inlineRadioOptions'] == 'GSM':
            file2G = request.FILES['File2G']  # .readlines()
            gen = 2
            decoded_file = file2G.read().decode('latin-1').splitlines()
            reader = csv.reader(decoded_file, delimiter=';')
            for row in reader:
                rowsFile.append(row)
            for i in range(len(rowsFile) - 1):
                cellnomer = ""
                cellnomer = rowsFile[i][5]
                bsName.append(rowsFile[i][9])
                bsnomer.append(rowsFile[i][2])
                cellName.append(rowsFile[i][5])  # Забираем Имя Селла
                # cellName.append(cellnomer[cellnomer.rfind('_') + 1:])  # Забираем только номер Селла
                channel.append(rowsFile[i][16])
                if len(rowsFile[i][8]) == 1:
                    bsic.append("0" + rowsFile[i][8])
                else:
                    bsic.append(rowsFile[i][8])

                cid.append(rowsFile[i][7])
                lac.append(rowsFile[i][6])
            file_data = bts2Create(bss, gen, bsName, bsnomer, cellName, channel, bsic, cid, lac)
            if type(file_data) != list:
                context = {'region': reg,
                           'allError': zip(file_data['errbsname'], file_data['errbs_dp'], file_data['errbs_pk'])}
                return render(request, 'btscreate.html', context)
            else:
                response = HttpResponse(file_data, content_type='application/text charset=utf-8')
                response[
                    'Content-Disposition'] = f'attachment; filename= Bts_file-2G-{dt.datetime.now().strftime("%d-%m-%Y-%H-%M-%S")}.nbf'
                return response
        # UMTS------------------------------------------------------------------------------------------------------
        if request.POST['inlineRadioOptions'] == 'UMTS':
            file3G = request.FILES['File3G']  # .readlines()
            decoded_file = file3G.read().decode('latin-1').splitlines()
            reader = csv.reader(decoded_file, delimiter=';')
            for row in reader:
                rowsFile.append(row)
            for i in range(len(rowsFile) - 1):
                cellnomer = ""
                cellnomer = rowsFile[i][8]
                bsName.append(rowsFile[i][9])
                bsnomer.append(rowsFile[i][2])
                cellName.append(rowsFile[i][8])  # Забираем Имя Селла
                # cellName.append(cellnomer[cellnomer.rfind('_') + 1:])  # Забираем только номер Селла
                channel.append(rowsFile[i][11])
                bsic.append(rowsFile[i][14])  # SCR
                cellid = rowsFile[i][6]
                # print(cellid)
                cid.append(rowsFile[i][4])
                # cid.append(rowsFile[i][2] + rowsFile[i][6][-1])
                # cid.append(rowsFile[i][2] + cellid[-1])

            file_data = bts3Create(bss, bsName, bsnomer, cellName, channel, cid, bsic)
            if type(file_data) != list:
                context = {'region': reg,
                           'allError': zip(file_data['errbsname'], file_data['errbs_dp'], file_data['errbs_pk'])}
                return render(request, 'btscreate.html', context)
            else:
                response = HttpResponse(file_data, content_type='application/text charset=utf-8')
                response[
                    'Content-Disposition'] = f'attachment; filename= Bts_file-3G-{dt.datetime.now().strftime("%d-%m-%Y-%H-%M-%S")}.nbf'
                return response
        # LTE -------------------------------------------------------------------------------------------------------

        if request.POST['inlineRadioOptions'] == 'LTE':
            file4G = request.FILES['File4G']
            decoded_file = file4G.read().decode('latin-1').splitlines()
            reader = csv.reader(decoded_file, delimiter=';')
            for row in reader:
                rowsFile.append(row)
            for i in range(len(rowsFile) - 1):
                cellnomer = ""
                cellnomer = rowsFile[i][6]  # Номер селла
                band.append(rowsFile[i][8])
                bsnomer.append(rowsFile[i][2])
                cellName.append(rowsFile[i][6])  # Забираем Имя Селла
                # cellName.append(cellnomer[cellnomer.rfind('_') + 1:])  # Забираем только номер Селла
                channel.append(rowsFile[i][10])
                bsic.append(rowsFile[i][14])  # PCI
                tac.append(rowsFile[i][7])  # PCI

                cid.append(rowsFile[i][2] + rowsFile[i][5])
            file_data = bts4Create(bss, band, bsnomer, cellName, channel, cid, bsic, tac)
            if type(file_data) != list:
                context = {'region': reg,
                           'allError': zip(file_data['errbsname'], file_data['errbs_dp'], file_data['errbs_pk'])}
                return render(request, 'btscreate.html', context)
            else:
                response = HttpResponse(file_data, content_type='application/text charset=utf-8')
                response[
                    'Content-Disposition'] = f'attachment; filename= Bts_file-4G-{dt.datetime.now().strftime("%d-%m-%Y-%H-%M-%S")}.nbf'
                return response
