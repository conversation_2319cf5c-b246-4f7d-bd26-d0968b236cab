{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="jumbotron">
    <p>
</div>
<body>
<table class="table table-striped">
    <tr>
        <!--    <tr bgcolor="#ffcc00">-->
        <td width="5"> №</td>
        <td width="17%">Наименование БС</td>
        <td width="8%">Номер БС</td>
        <!--        <td width="15%">Авария</td>-->
        <td width="15%">Алармы</td>
        {% if user.privilege_id == 1 %}
        <td width="25%">
            <!-- Пример отдельной кнопки danger -->
            <div class="btn-group">
                <button type="button" class="btn btn-warning dropdown-toggle" data-bs-toggle="dropdown"
                        aria-expanded="false">
                    Выберите область
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 1 %}">Андижанская область</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 2 %}">Бухарская область</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 3 %}">город Коканд</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 4 %}">город Ташкент</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 5 %}">Джиззахская область</a>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 6 %}">Кашкадарьинская область</a>
                    </li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 7 %}">Наваийская область</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 8 %}">Наманганская область</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 9 %}">Республика Каракалпакстан</a>
                    </li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 10 %}">Самаркандская область</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 11 %}">Сирдарьинская область</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 12 %}">Сурхандаьинская область</a>
                    </li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 13 %}">Ташкентская область</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 14 %}">Ферганская область</a></li>
                    <li><a class="dropdown-item" href="{% url 'alarmregionviewall' 15 %}">Хорезмская область</a></li>
                </ul>
            </div>

        </td>
        {% else %}
        <td width="25%">BSC/RNC</td>
        {% endif %}


        <td width="15%">Время Аварии</Td>
        <!--        <td width="15%">Время устранения</Td>-->
        <td width="15%">Время простоя</Td>
        <td><img src="{% static 'img/trash1.png' %}"></Td>
    </TR>

    {% for item in object_list %}

    <tr>
        <td>{{ forloop.counter0|add:page_obj.start_index }}</td>
        <td>{{ item.bsname }}</td>
        <td>{{ item.bsnumber }}</td>
        <td>{{ item.alarmname }}</td>
        <td>{{ item.cellname }}</td>
        <td>{{ item.appeartime|date:"Y-m-d H:m:s"}}</td>
        <!--        <td>{{ item.cleartime|date:"Y-m-d H:m:s"}}</td>-->
        <td>{{ item.calctime}}</td>
        {% if user.privilege_id != 3 %}
        <td><a href="{% url 'deletefromalarmall' item.pk %}"><img src="{% static 'img/trash1.png' %}" alt="Удалить"></a>
        </td>
        {% else %}
        <td><a href="{% url 'accessdeny' %}"><img src="{% static 'img/trash1.png' %}" alt="Удалить"></a></td>
        {% endif %}
    </tr>
    {% endfor %}
</table>
<div class="pagination">
    <span class="step-links">
        {% if page_obj.has_previous %}
            <a href="?page=1">&laquo; first</a>
            <a href="?page={{ page_obj.previous_page_number }}">previous</a>
        {% endif %}

        <span class="current">
            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}.
        </span>

        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}">next</a>
            <a href="?page={{ page_obj.paginator.num_pages }}">last &raquo;</a>
        {% endif %}
    </span>
</div>


</body>
{% endblock content %}
