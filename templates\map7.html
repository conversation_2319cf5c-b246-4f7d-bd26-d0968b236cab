{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% block title %} Map View BS Beeline {% endblock title %}

{% block content %}

<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
    integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
<link rel="stylesheet" href="/static/css/styles.css">

<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
    integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

<style>
    /* Body stillarini o'rnatamiz */
    body {
        margin: 0;
        padding: 0;
        overflow: hidden;
    }

    /* Navbar stillarini o'zgartiramiz */
    .navbar {
        margin-bottom: 0 !important;
        z-index: 2000;
        position: relative;
    }

    /* Карта контейнери учун стиллар */
    #mapid {
        height: 100vh;
        width: calc(100% - 300px);
        margin: 0;
        padding: 0;
        position: fixed;
        top: 0;
        left: 300px;
        right: 0;
        bottom: 0;
        z-index: 95;
    }

    /* Zoom tugmalarini to'g'rilash */
    .leaflet-control-container .leaflet-top {
        top: 80px;
    }

    .leaflet-control-container .leaflet-right {
        right: 10px;
    }

    .leaflet-control-zoom {
        margin-right: 10px;
    }

    /* Bo'sh joyni yo'qotish uchun */
    .container-fluid {
        padding: 0;
        margin: 0;
        position: relative;
    }

    /* Стиль для сайдбара */
    .sidebar {
        width: 300px;
        height: calc(100vh - 70px);
        background-color: white;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: fixed;
        top: 70px;
        left: 0;
        z-index: 1000;
        border-radius: 0;
    }

    .sidebar-header {
        padding: 15px;
        background-color: #2c3e50;
        color: white;
    }

    .sidebar-header h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
    }

    .sidebar-content {
        padding: 15px;
        overflow-y: auto;
        flex: 1;
    }

    .stats-container {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        background-color: #f5f5f5;
        border-radius: 5px;
        padding: 10px;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 12px;
        color: #666;
    }

    .stat-item.online .stat-value {
        color: #4caf50;
    }

    .stat-item.offline .stat-value {
        color: #f44336;
    }

    .filter-group {
        margin-bottom: 15px;
    }

    .filter-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        font-size: 14px;
        color: #333;
    }

    .filter-group select {
        width: 100%;
        padding: 8px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .filter-group select:focus {
        outline: none;
        border-color: #2c3e50;
    }

    .filter-group select:disabled {
        background-color: #f5f5f5;
        cursor: not-allowed;
    }

    .map-type-control {
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
        padding: 5px;
        display: flex;
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
    }

    .map-type-control button {
        background-color: white;
        border: 1px solid #ccc;
        padding: 6px 10px;
        cursor: pointer;
        font-size: 12px;
        outline: none;
    }

    .map-type-control button:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        border-right: none;
    }

    .map-type-control button:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }

    .map-type-control button.active {
        background-color: #f0f0f0;
        font-weight: bold;
    }
</style>
</head>

<body>

    <div class="container-fluid">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>Фильтры</h2>
            </div>
            <div class="sidebar-content">
                <!-- Статистика -->
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-value" id="total-bs-count">0</div>
                        <div class="stat-label">Всего БС</div>
                    </div>
                    <div class="stat-item online">
                        <div class="stat-value" id="active-bs-count">0</div>
                        <div class="stat-label">Онлайн</div>
                    </div>
                    <div class="stat-item offline">
                        <div class="stat-value" id="inactive-bs-count">0</div>
                        <div class="stat-label">Оффлайн</div>
                    </div>
                </div>

                <!-- Выбор региона -->
                <div class="filter-group">
                    <label for="region">Регион:</label>
                    <select id="region">
                        <option value="">Все регионы</option>
                        <!-- Регионы будут добавлены динамически -->
                    </select>
                </div>

                <!-- Выбор района -->
                <div class="filter-group">
                    <label for="area">Район:</label>
                    <select id="area" disabled>
                        <option value="">Все районы</option>
                        <!-- Районы будут добавлены динамически -->
                    </select>
                </div>

                <!-- Поиск по имени или номеру БС -->
                <div class="filter-group">
                    <label for="bs-search">Поиск БС:</label>
                    <input type="text" id="bs-search" class="form-control" placeholder="Введите название или номер БС">
                </div>

                <!-- Выбор статуса -->
                <div class="filter-group">
                    <label for="status">Статус:</label>
                    <select id="status">
                        <option value="all">Все</option>
                        <option value="online">Онлайн</option>
                        <option value="offline">Оффлайн</option>
                    </select>
                </div>

                <div class="filter-group">
                    <button id="reset-filters" class="btn btn-danger btn-sm w-100 mb-3">Сбросить фильтры</button>
                </div>

                <div class="mt-3">
                    <button id="map-button" class="btn btn-primary btn-sm">Карта</button>
                    <button id="satellite-button" class="btn btn-secondary btn-sm">Спутник</button>
                </div>
            </div>
        </div>
    </div>

    <div id="mapid"></div>

    {{ points_data_json|json_script:"points-data" }}

    <script>
        // Фильтр қийматларини сақлаш учун функция
        function saveFilterState() {
            const filterState = {
                region: document.getElementById('region').value,
                area: document.getElementById('area').value,
                bsSearch: document.getElementById('bs-search').value,
                status: document.getElementById('status').value
            };
            localStorage.setItem('filterState', JSON.stringify(filterState));
        }

        // Фильтр қийматларини тиклаш учун функция
        function restoreFilterState() {
            const savedState = localStorage.getItem('filterState');
            if (savedState) {
                const filterState = JSON.parse(savedState);
                const regionId = filterState.region;
                const areaId = filterState.area;
                const bsSearch = filterState.bsSearch || '';
                const statusValue = filterState.status;

                // Устанавливаем значения в селектах и полях
                document.getElementById('region').value = regionId;
                document.getElementById('bs-search').value = bsSearch;
                document.getElementById('status').value = statusValue;

                if (regionId) {
                    // Загружаем районы для выбранного региона
                    fetch(`/api/areas/region/${regionId}/`)
                        .then(response => response.json())
                        .then(areas => {
                            const areaSelect = document.getElementById('area');
                            areaSelect.innerHTML = '<option value="">Все районы</option>';
                            areas.forEach(area => {
                                const option = document.createElement('option');
                                option.value = area.id;
                                option.textContent = area.name;
                                areaSelect.appendChild(option);
                            });
                            areaSelect.disabled = false;
                            areaSelect.value = areaId;

                            // Определяем источник данных в зависимости от фильтров
                            let dataPromise;
                            
                            if (areaId) {
                                // Если выбран район
                                dataPromise = fetch(`/api/base-stations/area/${areaId}/`)
                                    .then(response => response.json());
                            } else {
                                // Если выбран только регион
                                dataPromise = fetch(`/api/base-stations/region/${regionId}/`)
                                    .then(response => response.json());
                            }

                            dataPromise.then(stations => {
                                if (stations.length > 0) {
                                    // Умумий БС маълумотларидан статус қийматларини олиш
                                    const stationStatusMap = {};
                                    points.forEach(point => {
                                        // БС номи бўйича статус қийматини сақлаш
                                        stationStatusMap[point.bsName || point.name] = point.status;
                                    });

                                    stations = stations.map(station => {
                                        // БС номи бўйича умумий маълумотлардан статусни олиш
                                        const stationName = station.bsName || station.name;
                                        const statusFromGlobal = stationStatusMap[stationName];

                                        return {
                                            ...station,
                                            // Агар умумий маълумотларда статус топилса, шуни ишлатиш, акс ҳолда API дан қайтган статусни ишлатиш
                                            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                        };
                                    });

                                    // Если задан поиск по имени БС, применяем фильтр
                                    if (bsSearch) {
                                        stations = filterByBsName(stations, bsSearch);
                                    }

                                    // Если есть отфильтрованные станции
                                    if (stations.length > 0) {
                                        // Вычисляем центр
                                        let totalLat = 0;
                                        let totalLon = 0;
                                        stations.forEach(station => {
                                            totalLat += parseFloat(station.lat);
                                            totalLon += parseFloat(station.lon);
                                        });
                                        const centerLat = totalLat / stations.length;
                                        const centerLon = totalLon / stations.length;

                                        // Центрируем карту
                                        const zoomLevel = areaId ? 11 : 10;
                                        mymap.setView([centerLat, centerLon], zoomLevel);

                                        // Применяем фильтр по статусу
                                        applyStatusFilter(stations, statusValue);
                                    } else {
                                        // Если нет станций после фильтрации по имени
                                        pointLayers.forEach(marker => mymap.removeLayer(marker));
                                        pointLayers.length = 0;
                                        
                                        // Обновляем статистику
                                        document.getElementById('total-bs-count').textContent = '0';
                                        document.getElementById('active-bs-count').textContent = '0';
                                        document.getElementById('inactive-bs-count').textContent = '0';
                                    }
                                }
                            });
                        });
                } else {
                    // Если регион не выбран, применяем фильтры к общим данным
                    let filteredPoints = [...points];
                    
                    // Если задан поиск по имени БС, применяем фильтр
                    if (bsSearch) {
                        filteredPoints = filterByBsName(filteredPoints, bsSearch);
                    }
                    
                    // Применяем фильтр по статусу
                    applyStatusFilter(filteredPoints, statusValue);
                }
            } else {
                // Если нет сохраненных фильтров, показываем все точки
                applyStatusFilter(points, 'all');
            }
        }

        // Маълумотларни янгилаш функцияси
        function refreshData() {
            // Сохраняем текущее состояние фильтров перед обновлением
            const currentRegion = document.getElementById('region').value;
            const currentArea = document.getElementById('area').value;
            const currentBsSearch = document.getElementById('bs-search').value;
            const currentStatus = document.getElementById('status').value;

            // Сохраняем состояние в localStorage
            saveFilterState();

            fetch(window.location.href)
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newPointsData = JSON.parse(doc.getElementById('points-data').textContent);
                    
                    // Сохраняем старые данные для сравнения
                    const oldPoints = [...points];
                    
                    // Получаем все точки для дальнейшей фильтрации
                    points = newPointsData;

                    // Обновляем общую статистику (умумий статистика)
                    totalBsCount = points.length;
                    inactiveBsCount = points.filter(point => point.status === true).length;
                    activeBsCount = totalBsCount - inactiveBsCount;

                    // Восстанавливаем состояние фильтров
                    document.getElementById('region').value = currentRegion;
                    document.getElementById('area').value = currentArea;
                    document.getElementById('bs-search').value = currentBsSearch;
                    document.getElementById('status').value = currentStatus;

                    // Применяем фильтры в зависимости от текущего состояния
                    const regionId = currentRegion ? parseInt(currentRegion) : null;
                    const areaId = currentArea ? parseInt(currentArea) : null;

                    // Применяем фильтры в зависимости от выбранных значений
                    if (regionId) {
                        // Если выбран регион
                        if (areaId) {
                            // Если выбран район
                            fetch(`/api/base-stations/area/${areaId}/`)
                                .then(response => response.json())
                                .then(stations => {
                                    // Умумий БС маълумотларидан статус қийматларини олиш
                                    const stationStatusMap = {};
                                    points.forEach(point => {
                                        // БС номи бўйича статус қийматини сақлаш
                                        stationStatusMap[point.bsName || point.name] = point.status;
                                    });

                                    stations = stations.map(station => {
                                        // БС номи бўйича умумий маълумотлардан статусни олиш
                                        const stationName = station.bsName || station.name;
                                        const statusFromGlobal = stationStatusMap[stationName];

                                        return {
                                            ...station,
                                            // Агар умумий маълумотларда статус топилса, шуни ишлатиш, акс ҳолда API дан қайтган статусни ишлатиш
                                            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                        };
                                    });

                                    // Если задан поиск по имени БС, применяем фильтр
                                    if (currentBsSearch) {
                                        stations = filterByBsName(stations, currentBsSearch);
                                    }

                                    // Применяем фильтр по статусу к станциям района
                                    applyStatusFilter(stations, currentStatus, true);
                                });
                        } else {
                            // Если выбран только регион
                            fetch(`/api/base-stations/region/${regionId}/`)
                                .then(response => response.json())
                                .then(stations => {
                                    // Умумий БС маълумотларидан статус қийматларини олиш
                                    const stationStatusMap = {};
                                    points.forEach(point => {
                                        // БС номи бўйича статус қийматини сақлаш
                                        stationStatusMap[point.bsName || point.name] = point.status;
                                    });

                                    stations = stations.map(station => {
                                        // БС номи бўйича умумий маълумотлардан статусни олиш
                                        const stationName = station.bsName || station.name;
                                        const statusFromGlobal = stationStatusMap[stationName];

                                        return {
                                            ...station,
                                            // Агар умумий маълумотларда статус топилса, шуни ишлатиш, акс ҳолда API дан қайтган статусни ишлатиш
                                            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                        };
                                    });

                                    // Если задан поиск по имени БС, применяем фильтр
                                    if (currentBsSearch) {
                                        stations = filterByBsName(stations, currentBsSearch);
                                    }

                                    // Применяем фильтр по статусу к станциям региона
                                    applyStatusFilter(stations, currentStatus, true);
                                });
                        }
                    } else {
                        // Если не выбран ни регион, ни район
                        // Фильтруем по имени/номеру БС если задано
                        let filteredPoints = [...points];
                        if (currentBsSearch) {
                            filteredPoints = filterByBsName(filteredPoints, currentBsSearch);
                            // Если есть результаты поиска и они есть на карте, обновляем только изменившиеся
                            if (filteredPoints.length > 0) {
                                updateChangedMarkers(oldPoints, filteredPoints, currentStatus);
                            } else {
                                // Очищаем карту если нет результатов
                                pointLayers.forEach(marker => mymap.removeLayer(marker));
                                pointLayers.length = 0;
                                
                                // Обновляем статистику
                                document.getElementById('total-bs-count').textContent = '0';
                                document.getElementById('active-bs-count').textContent = '0';
                                document.getElementById('inactive-bs-count').textContent = '0';
                            }
                        } else {
                            // Обновляем только маркеры, у которых изменился статус
                            updateChangedMarkers(oldPoints, points, currentStatus);
                        }
                    }
                })
                .catch(error => {
                    console.error('Маълумотларни янгилашда хато:', error);
                    // Хато бўлганда ҳам эски маркерларни сақлаб қолиш учун
                    if (points && points.length > 0) {
                        const statusValue = document.getElementById('status').value;
                        applyStatusFilter(points, statusValue, true);
                    }
                });
        }

        // Функция для фильтрации БС по имени или номеру
        function filterByBsName(stations, searchText) {
            searchText = searchText.toLowerCase();
            return stations.filter(station => {
                const bsName = (station.bsName || station.name || '').toLowerCase();
                const bsNumber = (station.bsNumber || station.number || '').toString().toLowerCase();
                return bsName.includes(searchText) || bsNumber.includes(searchText);
            });
        }

        // Функция для обновления только изменившихся маркеров (для режима все регионы)
        function updateChangedMarkers(oldPoints, newPoints, statusValue) {
            // Создаем карту для быстрого поиска точек по имени
            const oldPointsMap = {};
            oldPoints.forEach(point => {
                const pointName = point.bsName || point.name;
                oldPointsMap[pointName] = point;
            });
            
            // Фильтруем точки в соответствии с выбранным статусом
            let filteredNewPoints = [...newPoints];
            if (statusValue === 'online') {
                filteredNewPoints = newPoints.filter(point => point.status === false);
            } else if (statusValue === 'offline') {
                filteredNewPoints = newPoints.filter(point => point.status === true);
            }
            
            // Обновляем статистику
            const totalDisplayed = filteredNewPoints.length;
            const inactiveDisplayed = filteredNewPoints.filter(point => point.status === true).length;
            const activeDisplayed = totalDisplayed - inactiveDisplayed;
            
            document.getElementById('total-bs-count').textContent = totalDisplayed;
            document.getElementById('active-bs-count').textContent = activeDisplayed;
            document.getElementById('inactive-bs-count').textContent = inactiveDisplayed;
            
            // Создаем карту точек на карте по имени для быстрого доступа
            const markersMap = {};
            pointLayers.forEach(marker => {
                const pointName = marker.options.pointName;
                markersMap[pointName] = marker;
            });
            
            // Проверяем и обновляем существующие маркеры
            filteredNewPoints.forEach(point => {
                const pointName = point.bsName || point.name;
                
                // Если этот маркер уже есть на карте - обновляем его
                if (markersMap[pointName]) {
                    const marker = markersMap[pointName];
                    const oldPoint = oldPointsMap[pointName];
                    
                    // Проверяем, изменился ли статус
                    if (oldPoint && oldPoint.status !== point.status) {
                        // Изменяем цвет маркера
                        const newColor = point.status === true ? 'red' : 'green';
                        marker.setStyle({ fillColor: newColor });
                        
                        // Обновляем всплывающую подсказку
                        const statusText = point.status === true ? 'БС не работает' : 'БС активен';
                        marker.setTooltipContent("<b>" + pointName + "</b><br>" + statusText);
                        
                        // Выделяем изменившиеся маркеры анимацией
                        marker.setStyle({ fillOpacity: 1 });
                        setTimeout(() => {
                            marker.setStyle({ fillOpacity: 0.8 });
                        }, 500);
                    }
                    // Помечаем маркер как обработанный
                    markersMap[pointName] = null;
                } 
                // Если такого маркера нет, создаем новый
                else {
                    // Координаталар тўғри эканини текшириш
                    if (!point.lat || !point.lon || isNaN(parseFloat(point.lat)) || isNaN(parseFloat(point.lon))) {
                        console.warn('Нотўғри координаталар:', point);
                        return; // Бу нуқтани ўтказиб юбориш
                    }
                    
                    let pointColor = point.status === true ? 'red' : 'green';
                    let statusText = point.status === true ? 'БС не работает' : 'БС активен';

                    const circleMarker = L.circleMarker([point.lat, point.lon], {
                        radius: getCircleRadius(mymap.getZoom()),
                        fillColor: pointColor,
                        color: "#000",
                        weight: 1,
                        opacity: 1,
                        fillOpacity: 0.8,
                        pointName: pointName // Сохраняем имя точки в опциях маркера
                    })
                        .bindTooltip("<b>" + pointName + "</b><br>" + statusText, {
                            permanent: false,
                            direction: 'top',
                            offset: [0, -10]
                        })
                        .addTo(mymap);

                    pointLayers.push(circleMarker);
                }
            });
            
            // Удаляем маркеры, которых больше нет в новых данных
            const newPointNames = filteredNewPoints.map(p => p.bsName || p.name);
            
            for (let i = pointLayers.length - 1; i >= 0; i--) {
                const marker = pointLayers[i];
                const pointName = marker.options.pointName;
                
                if (!newPointNames.includes(pointName)) {
                    mymap.removeLayer(marker);
                    pointLayers.splice(i, 1);
                }
            }
        }
        
        // Функция для применения фильтра по статусу и отображения точек
        function applyStatusFilter(pointsToFilter, statusValue, isRefresh = false) {
            // Если это не рефреш или выбран регион/район, удаляем все маркеры и создаем заново
            if (!isRefresh || document.getElementById('region').value) {
                // Удаляем все существующие маркеры с карты
                pointLayers.forEach(marker => mymap.removeLayer(marker));
                pointLayers.length = 0;
            }

            console.log('Фильтрга келган маълумотлар:', pointsToFilter);
            let displayPoints = [...pointsToFilter];
            
            // Фильтр по статусу - это второй уровень фильтрации после региона/района
            if (statusValue === 'online') {
                displayPoints = pointsToFilter.filter(point => point.status === false);
                console.log('Онлайн фильтр натижаси:', displayPoints);
            } else if (statusValue === 'offline') {
                displayPoints = pointsToFilter.filter(point => point.status === true);
                console.log('Оффлайн фильтр натижаси:', displayPoints);
            }
            
            // Обновляем только локальную статистику для текущего выбора
            const totalDisplayed = displayPoints.length;
            const inactiveDisplayed = displayPoints.filter(point => point.status === true).length;
            const activeDisplayed = totalDisplayed - inactiveDisplayed;
            console.log('Фильтр натижаси - Жами:', totalDisplayed, 'Онлайн:', activeDisplayed, 'Оффлайн:', inactiveDisplayed);

            // Обновляем статистику в сайдбаре
            document.getElementById('total-bs-count').textContent = totalDisplayed;
            document.getElementById('active-bs-count').textContent = activeDisplayed;
            document.getElementById('inactive-bs-count').textContent = inactiveDisplayed;

            // Если это рефреш и не выбран регион/район, обновление маркеров выполняет updateChangedMarkers
            if (isRefresh && !document.getElementById('region').value) {
                return;
            }

            // Отображаем отфильтрованные точки на карте
            if (displayPoints && displayPoints.length > 0) {
                displayPoints.forEach(function (point) {
                    // Координаталар тўғри эканини текшириш
                    if (!point.lat || !point.lon || isNaN(parseFloat(point.lat)) || isNaN(parseFloat(point.lon))) {
                        console.warn('Нотўғри координаталар:', point);
                        return; // Бу нуқтани ўтказиб юбориш
                    }
                    
                    let pointColor = point.status === true ? 'red' : 'green';
                    let statusText = point.status === true ? 'БС не работает' : 'БС активен';
                    const pointName = point.bsName || point.name;

                    const circleMarker = L.circleMarker([point.lat, point.lon], {
                        radius: getCircleRadius(mymap.getZoom()),
                        fillColor: pointColor,
                        color: "#000",
                        weight: 1,
                        opacity: 1,
                        fillOpacity: 0.8,
                        pointName: pointName // Сохраняем имя точки в опциях маркера
                    })
                        .bindTooltip("<b>" + pointName + "</b><br>" + statusText, {
                            permanent: false,
                            direction: 'top',
                            offset: [0, -10]
                        })
                        .addTo(mymap);

                    pointLayers.push(circleMarker);
                });
                console.log('Картага қўшилган нуқталар сони:', pointLayers.length);
            } else {
                console.warn('Кўрсатиш учун нуқталар мавжуд эмас!');
            }
        }

        // Ҳар 10 секундда маълумотларни янгилаш
        setInterval(refreshData, 10000);

        // 1. Инициализация карты
        var mymap = L.map('mapid').setView([41.3, 69.3], 6) // Марказ Ўзбекистон - умумий кўриниш

        // 2. Добавление базового слоя карты (по умолчанию)
        var osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(mymap);

        // 3. Добавление спутникового слоя
        var satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            attribution: '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            maxZoom: 20
        });

        // 4. Получение данных о точках
        const pointsDataElement = document.getElementById('points-data');
        var points = JSON.parse(pointsDataElement.textContent);
        console.log('Умумий БС маълумотлари:', points);

        // Подсчет статистики БС
        var totalBsCount = points.length;
        var inactiveBsCount = points.filter(point => point.status === true).length;
        var activeBsCount = totalBsCount - inactiveBsCount;
        console.log('Умумий БС сони:', totalBsCount);
        console.log('Оффлайн БС сони:', inactiveBsCount);
        console.log('Онлайн БС сони:', activeBsCount);

        // Отображение статистики
        document.getElementById('total-bs-count').textContent = totalBsCount;
        document.getElementById('active-bs-count').textContent = activeBsCount;
        document.getElementById('inactive-bs-count').textContent = inactiveBsCount;

        // Функция для определения размера круга в зависимости от уровня зума
        function getCircleRadius(zoom) {
            if (zoom < 6) {
                return 2;
            } else if (zoom < 10) {
                return 4;
            } else {
                return 10;
            }
        }

        // Функция для обновления размера кругов на карте
        function updateCircleSizes() {
            const currentZoom = mymap.getZoom();
            const newRadius = getCircleRadius(currentZoom);

            mymap.eachLayer(function (layer) {
                if (layer instanceof L.CircleMarker) {
                    layer.setRadius(newRadius);
                }
            });
        }

        // Создаем и добавляем маркеры на карту
        const pointLayers = []; // Массив для хранения слоев маркеров
        const pointsByRegion = {}; // Объект для хранения точек по регионам
        const pointsByArea = {}; // Объект для хранения точек по районам

        // Координаты центров регионов
        const regionCenters = {
            1: [41.311081, 69.240562], // Тошкент шаҳри
            2: [40.783555, 72.350891], // Андижон
            3: [40.384240, 71.785690], // Фарғона
            4: [41.001071, 71.672278], // Наманган
            5: [39.768083, 64.421710], // Бухоро
            6: [40.121462, 67.842194], // Жиззах
            7: [38.839802, 65.781462], // Қашқадарё
            8: [40.103922, 65.374260], // Навоий
            9: [41.248990, 69.333240], // Тошкент вилояти
            10: [40.837641, 68.661338], // Сирдарё
            11: [37.940552, 67.510929], // Сурхондарё
            12: [41.552080, 60.631622], // Хоразм
            13: [43.804363, 59.018464], // Қорақалпоғистон
            14: [39.654388, 66.975824]  // Самарқанд
        };

        // Загрузка регионов
        fetch('/api/regions/')
            .then(response => response.json())
            .then(regions => {
                const regionSelect = document.getElementById('region');
                regions.forEach(region => {
                    const option = document.createElement('option');
                    option.value = region.id;
                    option.textContent = region.name;
                    regionSelect.appendChild(option);
                });
            });

        // Обработчик изменения региона
        document.getElementById('region').addEventListener('change', function () {
            const regionId = parseInt(this.value);
            const statusValue = document.getElementById('status').value;
            const bsSearch = document.getElementById('bs-search').value.trim();
            saveFilterState();
            const areaSelect = document.getElementById('area');

            // Очистка списка районов
            areaSelect.innerHTML = '<option value="">Все районы</option>';

            // Если выбрано "Все регионы" или значение пустое
            if (!regionId) {
                // Отключаем выбор района
                areaSelect.disabled = true;
                // Возвращаем карту к общему виду Узбекистана
                mymap.setView([41.3, 69.3], 6); // Марказ Ўзбекистон - умумий кўриниш

                // Применяем фильтр ко всем точкам
                let filteredPoints = [...points];
                
                // Применяем фильтр по имени/номеру БС если задан
                if (bsSearch) {
                    filteredPoints = filterByBsName(filteredPoints, bsSearch);
                }
                
                // Применяем фильтр по статусу
                applyStatusFilter(filteredPoints, statusValue);
                return;
            }

            if (regionId) {
                // Загрузка районов для выбранного региона
                fetch(`/api/areas/region/${regionId}/`)
                    .then(response => response.json())
                    .then(areas => {
                        areas.forEach(area => {
                            const option = document.createElement('option');
                            option.value = area.id;
                            option.textContent = area.name;
                            areaSelect.appendChild(option);
                        });
                        areaSelect.disabled = false;
                    });

                // Загрузка базовых станций для выбранного региона и центрирование карты
                fetch(`/api/base-stations/region/${regionId}/`)
                    .then(response => response.json())
                    .then(stations => {
                        if (stations.length > 0) {
                            // Синхронизируем статусы с глобальными данными
                            const stationStatusMap = {};
                            points.forEach(point => {
                                stationStatusMap[point.bsName || point.name] = point.status;
                            });

                            stations = stations.map(station => {
                                const stationName = station.bsName || station.name;
                                const statusFromGlobal = stationStatusMap[stationName];
                                return {
                                    ...station,
                                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                };
                            });
                            
                            // Фильтрация по имени/номеру БС если задано
                            let filteredStations = stations;
                            if (bsSearch) {
                                filteredStations = filterByBsName(stations, bsSearch);
                            }
                            
                            // Если есть станции после фильтрации
                            if (filteredStations.length > 0) {
                                // Вычисляем центр региона на основе координат станций
                                let totalLat = 0;
                                let totalLon = 0;
                                filteredStations.forEach(station => {
                                    totalLat += parseFloat(station.lat);
                                    totalLon += parseFloat(station.lon);
                                });
                                const centerLat = totalLat / filteredStations.length;
                                const centerLon = totalLon / filteredStations.length;

                                // Центрируем карту на вычисленном центре региона
                                mymap.setView([centerLat, centerLon], 10);

                                // Применяем фильтр по статусу к станциям региона
                                applyStatusFilter(filteredStations, statusValue);
                            } else {
                                // Если нет станций после фильтрации
                                pointLayers.forEach(marker => mymap.removeLayer(marker));
                                pointLayers.length = 0;
                                
                                // Обновляем статистику
                                document.getElementById('total-bs-count').textContent = '0';
                                document.getElementById('active-bs-count').textContent = '0';
                                document.getElementById('inactive-bs-count').textContent = '0';
                                
                                // Используем центр региона из заранее определенных координат
                                if (regionCenters[regionId]) {
                                    mymap.setView(regionCenters[regionId], 10);
                                }
                            }
                        }
                    });
            } else {
                // Если регион не выбран, отключаем выбор района
                areaSelect.disabled = true;

                // Возвращаем карту к общему виду
                mymap.setView([41.3, 69.3], 6);

                // Применяем фильтр ко всем точкам
                let filteredPoints = [...points];
                
                // Применяем фильтр по имени/номеру БС если задан
                if (bsSearch) {
                    filteredPoints = filterByBsName(filteredPoints, bsSearch);
                }
                
                // Применяем фильтр по статусу
                applyStatusFilter(filteredPoints, statusValue);
            }
        });

        // Обработчик изменения района
        document.getElementById('area').addEventListener('change', function () {
            const areaId = parseInt(this.value);
            const statusValue = document.getElementById('status').value;
            const bsSearch = document.getElementById('bs-search').value.trim();
            saveFilterState();

            if (areaId) {
                // Загрузка базовых станций для выбранного района
                fetch(`/api/base-stations/area/${areaId}/`)
                    .then(response => response.json())
                    .then(stations => {
                        if (stations.length > 0) {
                            // Синхронизируем статусы с глобальными данными
                            const stationStatusMap = {};
                            points.forEach(point => {
                                stationStatusMap[point.bsName || point.name] = point.status;
                            });

                            stations = stations.map(station => {
                                const stationName = station.bsName || station.name;
                                const statusFromGlobal = stationStatusMap[stationName];
                                return {
                                    ...station,
                                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                };
                            });
                            
                            // Фильтрация по имени/номеру БС если задано
                            let filteredStations = stations;
                            if (bsSearch) {
                                filteredStations = filterByBsName(stations, bsSearch);
                            }
                            
                            // Если есть станции после фильтрации
                            if (filteredStations.length > 0) {
                                // Вычисляем центр района на основе координат станций
                                let totalLat = 0;
                                let totalLon = 0;
                                filteredStations.forEach(station => {
                                    totalLat += parseFloat(station.lat);
                                    totalLon += parseFloat(station.lon);
                                });
                                const centerLat = totalLat / filteredStations.length;
                                const centerLon = totalLon / filteredStations.length;

                                // Центрируем карту на вычисленном центре района
                                mymap.setView([centerLat, centerLon], 11);

                                // Применяем фильтр по статусу к станциям района
                                applyStatusFilter(filteredStations, statusValue);
                            } else {
                                // Если нет станций после фильтрации
                                pointLayers.forEach(marker => mymap.removeLayer(marker));
                                pointLayers.length = 0;
                                
                                // Обновляем статистику
                                document.getElementById('total-bs-count').textContent = '0';
                                document.getElementById('active-bs-count').textContent = '0';
                                document.getElementById('inactive-bs-count').textContent = '0';
                            }
                        }
                    });
            } else {
                // Если район не выбран, возвращаемся к виду региона
                const regionId = parseInt(document.getElementById('region').value);
                if (regionId) {
                    // Загружаем станции для выбранного региона
                    fetch(`/api/base-stations/region/${regionId}/`)
                        .then(response => response.json())
                        .then(stations => {
                            if (stations.length > 0) {
                                // Синхронизируем статусы с глобальными данными
                                const stationStatusMap = {};
                                points.forEach(point => {
                                    stationStatusMap[point.bsName || point.name] = point.status;
                                });

                                stations = stations.map(station => {
                                    const stationName = station.bsName || station.name;
                                    const statusFromGlobal = stationStatusMap[stationName];
                                    return {
                                        ...station,
                                        status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                    };
                                });
                                
                                // Фильтрация по имени/номеру БС если задано
                                let filteredStations = stations;
                                if (bsSearch) {
                                    filteredStations = filterByBsName(stations, bsSearch);
                                }
                                
                                // Если есть станции после фильтрации
                                if (filteredStations.length > 0) {
                                    if (regionCenters[regionId]) {
                                        mymap.setView(regionCenters[regionId], 8);
                                    } else {
                                        // Вычисляем центр региона на основе координат станций
                                        let totalLat = 0;
                                        let totalLon = 0;
                                        filteredStations.forEach(station => {
                                            totalLat += parseFloat(station.lat);
                                            totalLon += parseFloat(station.lon);
                                        });
                                        const centerLat = totalLat / filteredStations.length;
                                        const centerLon = totalLon / filteredStations.length;
                                        mymap.setView([centerLat, centerLon], 8);
                                    }

                                    // Применяем фильтр по статусу к станциям региона
                                    applyStatusFilter(filteredStations, statusValue);
                                } else {
                                    // Если нет станций после фильтрации
                                    pointLayers.forEach(marker => mymap.removeLayer(marker));
                                    pointLayers.length = 0;
                                    
                                    // Обновляем статистику
                                    document.getElementById('total-bs-count').textContent = '0';
                                    document.getElementById('active-bs-count').textContent = '0';
                                    document.getElementById('inactive-bs-count').textContent = '0';
                                    
                                    // Используем центр региона из заранее определенных координат
                                    if (regionCenters[regionId]) {
                                        mymap.setView(regionCenters[regionId], 8);
                                    }
                                }
                            }
                        });
                } else {
                    // Если ни регион, ни район не выбраны
                    mymap.setView([41.3, 69.3], 6); // Марказ Ўзбекистон - умумий кўриниш

                    // Применяем фильтр ко всем точкам
                    let filteredPoints = [...points];
                    
                    // Применяем фильтр по имени/номеру БС если задан
                    if (bsSearch) {
                        filteredPoints = filterByBsName(filteredPoints, bsSearch);
                    }
                    
                    // Применяем фильтр по статусу
                    applyStatusFilter(filteredPoints, statusValue);
                }
            }
        });

        // Статистика теперь обновляется в функции applyStatusFilter

        // Функция для сброса всех фильтров
        function resetAllFilters() {
            // Сбрасываем значения в селектах
            document.getElementById('region').value = '';
            document.getElementById('area').value = '';
            document.getElementById('bs-search').value = '';
            document.getElementById('status').value = 'all';

            // Отключаем выбор района
            document.getElementById('area').disabled = true;

            // Возвращаем карту к общему виду Узбекистана
            mymap.setView([41.3, 69.3], 6);

            // Показываем все точки
            applyStatusFilter(points, 'all');

            // Сохраняем сброшенные фильтры в localStorage
            saveFilterState();
        }

        // Саҳифа юкланганда фильтр қийматларини тиклаш
        document.addEventListener('DOMContentLoaded', function () {
            // Восстанавливаем фильтры из localStorage
            restoreFilterState();

            // Добавляем обработчик события для кнопки сброса фильтров
            document.getElementById('reset-filters').addEventListener('click', resetAllFilters);
            
            // Обработчик события для поля поиска БС
            document.getElementById('bs-search').addEventListener('input', function() {
                const searchValue = this.value.trim();
                const regionId = parseInt(document.getElementById('region').value) || null;
                const areaId = parseInt(document.getElementById('area').value) || null;
                const statusValue = document.getElementById('status').value;
                
                // Сохраняем состояние фильтров
                saveFilterState();
                
                // Удаляем все маркеры с карты
                pointLayers.forEach(marker => mymap.removeLayer(marker));
                pointLayers.length = 0;
                
                // Выбираем источник данных для фильтрации
                let dataToFilter;
                if (regionId) {
                    if (areaId) {
                        // Если выбран район, загружаем данные района
                        fetch(`/api/base-stations/area/${areaId}/`)
                            .then(response => response.json())
                            .then(stations => {
                                // Синхронизируем статусы с глобальными данными
                                const stationStatusMap = {};
                                points.forEach(point => {
                                    stationStatusMap[point.bsName || point.name] = point.status;
                                });
                                
                                stations = stations.map(station => {
                                    const stationName = station.bsName || station.name;
                                    const statusFromGlobal = stationStatusMap[stationName];
                                    return {
                                        ...station,
                                        status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                    };
                                });
                                
                                // Применяем фильтр по имени/номеру БС
                                if (searchValue) {
                                    stations = filterByBsName(stations, searchValue);
                                }
                                
                                // Применяем фильтр по статусу
                                applyStatusFilter(stations, statusValue);
                            });
                    } else {
                        // Если выбран только регион, загружаем данные региона
                        fetch(`/api/base-stations/region/${regionId}/`)
                            .then(response => response.json())
                            .then(stations => {
                                // Синхронизируем статусы с глобальными данными
                                const stationStatusMap = {};
                                points.forEach(point => {
                                    stationStatusMap[point.bsName || point.name] = point.status;
                                });
                                
                                stations = stations.map(station => {
                                    const stationName = station.bsName || station.name;
                                    const statusFromGlobal = stationStatusMap[stationName];
                                    return {
                                        ...station,
                                        status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                    };
                                });
                                
                                // Применяем фильтр по имени/номеру БС
                                if (searchValue) {
                                    stations = filterByBsName(stations, searchValue);
                                }
                                
                                // Применяем фильтр по статусу
                                applyStatusFilter(stations, statusValue);
                            });
                    }
                } else {
                    // Если не выбран ни регион, ни район, фильтруем все точки
                    let filteredPoints = [...points];
                    
                    // Применяем фильтр по имени/номеру БС
                    if (searchValue) {
                        filteredPoints = filterByBsName(filteredPoints, searchValue);
                    }
                    
                    // Применяем фильтр по статусу
                    applyStatusFilter(filteredPoints, statusValue);
                }
            });
        });

        // Обработчик изменения статуса
        document.getElementById('status').addEventListener('change', function () {
            const statusValue = this.value;
            const regionId = parseInt(document.getElementById('region').value) || null;
            const areaId = parseInt(document.getElementById('area').value) || null;
            const bsSearch = document.getElementById('bs-search').value.trim();
            
            saveFilterState();

            // Удаляем все маркеры с карты
            pointLayers.forEach(marker => mymap.removeLayer(marker));
            pointLayers.length = 0;

            // Применяем фильтры в зависимости от выбранных значений
            if (regionId) {
                // Если выбран регион
                if (areaId) {
                    // Если выбран район
                    fetch(`/api/base-stations/area/${areaId}/`)
                        .then(response => response.json())
                        .then(stations => {
                            // Синхронизируем статусы с глобальными данными
                            const stationStatusMap = {};
                            points.forEach(point => {
                                stationStatusMap[point.bsName || point.name] = point.status;
                            });
                            
                            stations = stations.map(station => {
                                const stationName = station.bsName || station.name;
                                const statusFromGlobal = stationStatusMap[stationName];
                                return {
                                    ...station,
                                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                };
                            });
                            
                            // Применяем фильтр по имени/номеру БС
                            if (bsSearch) {
                                stations = filterByBsName(stations, bsSearch);
                            }
                            
                            // Применяем фильтр по статусу
                            applyStatusFilter(stations, statusValue);
                        });
                } else {
                    // Если выбран только регион
                    fetch(`/api/base-stations/region/${regionId}/`)
                        .then(response => response.json())
                        .then(stations => {
                            // Синхронизируем статусы с глобальными данными
                            const stationStatusMap = {};
                            points.forEach(point => {
                                stationStatusMap[point.bsName || point.name] = point.status;
                            });
                            
                            stations = stations.map(station => {
                                const stationName = station.bsName || station.name;
                                const statusFromGlobal = stationStatusMap[stationName];
                                return {
                                    ...station,
                                    status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
                                };
                            });
                            
                            // Применяем фильтр по имени/номеру БС
                            if (bsSearch) {
                                stations = filterByBsName(stations, bsSearch);
                            }
                            
                            // Применяем фильтр по статусу
                            applyStatusFilter(stations, statusValue);
                        });
                }
            } else {
                // Если не выбран ни регион, ни район
                let filteredPoints = [...points];
                
                // Применяем фильтр по имени/номеру БС
                if (bsSearch) {
                    filteredPoints = filterByBsName(filteredPoints, bsSearch);
                }
                
                // Применяем фильтр по статусу ко всем точкам
                applyStatusFilter(filteredPoints, statusValue);
            }
        });

        // Обработчики кликов на кнопки переключения типа карты
        const mapButton = document.getElementById('map-button');
        const satelliteButton = document.getElementById('satellite-button');

        mapButton.addEventListener('click', function () {
            mymap.removeLayer(satelliteLayer);
            osmLayer.addTo(mymap);
            mapButton.classList.add('btn-primary');
            mapButton.classList.remove('btn-secondary');
            satelliteButton.classList.add('btn-secondary');
            satelliteButton.classList.remove('btn-primary');
        });

        satelliteButton.addEventListener('click', function () {
            mymap.removeLayer(osmLayer);
            satelliteLayer.addTo(mymap);
            satelliteButton.classList.add('btn-primary');
            satelliteButton.classList.remove('btn-secondary');
            mapButton.classList.add('btn-secondary');
            mapButton.classList.remove('btn-primary');
        });

        // Слушаем событие 'zoomend'
        mymap.on('zoomend', updateCircleSizes);

        // Ўзбекистон чегаралари
        const uzbekistanBounds = [
            [37.1, 55.9], // Жанубий-ғарбий нуқта
            [45.6, 73.1]  // Шимолий-шарқий нуқта
        ];
        // Картани Ўзбекистон билан чегаралаш
        mymap.setMaxBounds(uzbekistanBounds);
    </script>

</body>

{% endblock content %}