from django.urls import path
from bsview.views.alarmsAll import *
from bsview.views.bsviewall import *
from bsview.views.btskmzAll import *
from bsview.views.confFiles import *
from bsview.views.importFromFile import *
from bsview.views.reportsAll import *
from bsview.views.nri import *
from bsview.views.viewmaps import (
    map_view, get_base_stations_by_region, get_base_stations_by_area,
    get_all_regions, get_all_areas, get_areas_by_region, get_all_alarms
)




urlpatterns = [
    path('', BsPageView.as_view(), name='bsview'),
    path('<int:pk>/', BsDetailView.as_view(), name='viewdetail'),
    path('<int:pk>/edit/', BsUpdate.as_view(), name='bsedit'),
    path('<int:region>', BsRegionView.as_view(), name='bsregionview'),
    path('<int:pk>/delete/', BsDeleteView.as_view(), name='bsdelete'),
    path('searchbyname/', search_page1, name='search_page1'),
    path('searchbynumber/', search_page2, name='search_page2'),
    path('addbs/', addBs, name='addbs'),
    path('editbs/', editBs, name='editbs'),
    path('accessdeny/', accessdeny, name='accessdeny'),
    path('ajax/load-areas/', load_areas, name='ajax_load_areas'),
    path('createkmz/', createKMZ, name='createkmz'),
    path('createGnet/', createGnet, name='createGnet'),
    path('createbts/', createBTS, name='createbts'),
    path('createreport/', createReport, name='createreport'),
    path('getkmz/', getKMZ, name='getkmz'),
    path('getgnet/', getGnet, name='getgnet'),
    path('getbts/', getBTS, name='getbts'),
    path('getreport/', getReport, name='getreport'),
    path('closewindow/', closeWindow, name='closewindow'),
    path('getalarms/', BsAlarmView.as_view(), name='getalarms'),
    path('alarmsall/', BsAlarmViewAll.as_view(), name='alarmsall'),
    path('alarmsallselect/<int:alarms>', BsAlarmViewAllSelect.as_view(), name='alarmsallselect'),
    path('<int:pk>/almdelete/', AlarmDeleteView.as_view(), name='alarmdelete'),
    path('<int:pk>/deletefromalarmall/', AlarmDeleteViewAll.as_view(), name='deletefromalarmall'),
    path('alarms/<int:region>', AlarmRegionView.as_view(), name='alarmregionview'),
    path('alarmregionviewall/<int:region>', AlarmRegionViewAll.as_view(), name='alarmregionviewall'),
    path('alarmview/', alarmview, name='alarmview'),
    path('alarmanalyze/', analyze_alarm, name='alarmanalyze'),
    path('alarmanalyzeview/', alarm_analyze_view, name='alarmanalyzeview'),
    path('alarmanalyzeday/', analyze_alarm_day, name='alarmanalyzeday'),
    path('alarmanalyzeviewday/', alarms_view, name='alarmanalyzeviewday'),
    # path('alarmanalyzeviewday/', log_alarms_filtered, name='alarmanalyzeviewday'),
    # path('alarmanalyzeviewday/', alarm_analyze_view_day, name='alarmanalyzeviewday'),
    path('getalarm/', getalarm, name='getalarm'),
    path('alarmstat/', alarmstat, name='alarmstat'),
    path('idlebts/', idlebts, name='idlebts'),
    #path('search_alarms/', SearchAlarm.as_view(), name='searchalarms'),
    path('searchalarms/', searchAlarm, name='searchalarms'),
    path('importdata/', importData, name='importdata'),
    path('getfromexcel/', getfromexcel, name='getfromexcel'),
    path('conffiles/', conf_files, name='conffiles'),
    path('nriview/', nri_view, name='nriview'),
    path('getfromnri/', get_from_nri, name='getfromnri'),
    path('delete_selected/', delete_selected, name='delete_selected'),
    # path('mapview3/', uzb_map, name='mapview1'),
    path('mapview2/', map_view, name='mapview2'),
    # path('api/base-stations/', get_all_base_stations, name='base-stations'),
    path('api/base-stations/region/<int:region_id>/', get_base_stations_by_region,
         name='base-stations-by-region'),
    path('api/base-stations/area/<int:area_id>/', get_base_stations_by_area, name='base-stations-by-area'),
    # path('api/base-stations/status/<str:status_value>/', get_base_stations_by_status,
    #      name='base-stations-by-status'),
    path('api/regions/', get_all_regions, name='regions'),
    path('api/areas/', get_all_areas, name='areas'),
    path('api/areas/region/<int:region_id>/', get_areas_by_region, name='areas-by-region'),
    path('api/alarms/', get_all_alarms, name='alarms'),
    # path('upload/', upload_excel, name='upload'),
]

